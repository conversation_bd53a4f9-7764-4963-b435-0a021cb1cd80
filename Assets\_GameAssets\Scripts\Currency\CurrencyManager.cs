using DevNote;
using System;
using UnityEngine;
using Zenject;

public class CurrencyManager : ICurrencyManager
{
    private ISave _saveService;
    public event Action<int> OnBalanceChanged;

    [Inject]
    public CurrencyManager(ISave saveService)
    {
        _saveService = saveService;
 
        GameState.PlayerCurrency.OnChanged += () => OnBalanceChanged?.Invoke(GameState.PlayerCurrency.Value);
    }

    public int Balance => GameState.PlayerCurrency.Value;

    public void Add(int amount)
    {
        if (amount <= 0) return;

        GameState.PlayerCurrency.Value += amount;
        Save();
    }

    public bool Spend(int amount)
    {
        if (amount <= 0 || Balance < amount)
            return false;

        GameState.PlayerCurrency.Value -= amount;
        Save();
        return true;
    }

    private void Save()
    {
        _saveService?.SaveLocal(
            onSuccess: () => { },
            onError: () => Debug.LogError("Currency save error")
        );
    }
}